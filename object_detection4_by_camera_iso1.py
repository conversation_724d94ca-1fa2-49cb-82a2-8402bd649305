# Copyright (c) OpenMMLab. All rights reserved.
import os
import sys
import random
import time
from pathlib import Path
from argparse import ArgumentParser

import cv2
import mmcv
import numpy as np
import torch
from PIL import Image
from typing import List, Tuple

# Custom module imports
sys.path.append(str(Path(__file__).resolve().parents[3]))
sys.path.append('/home/<USER>/panpan/mm_detection/test_carm')

from projects.easydeploy.model import ORTWrapper  # noqa: E402
from utils import (  # noqa: E402
    bbox_postprocess, 
    preprocess, 
    visualize_detections,
    reg_max2bbox,
    resize_and_pad
)
from decode import (  # noqa: E402
    predict_by_feat,
    _bbox_post_process,
    get_single_pred,
    
)
from easydict import EasyDict
from pixel_to_physical_py.src.detection_processor import DetectionProcessor, DetectionResult, BBox
from pixel_to_physical_py.src.camera_coordinate_converter import CameraCoordinateConverter, CameraCoordinate

# Configuration and Constants
COLORS = [[random.randint(0, 255) for _ in range(3)] for _ in range(1000)]
CLASS_NAMES = ['bin', 'cloth', 'rug', 'shoe', 'wire', 'rail', 'wheel']
def transform_point(point, scale_factor, pad_param):
    """
    将模型输出的点坐标转换到原始图像坐标系
    Args:
        point: 元组 (x, y, conf)
        scale_factor: 缩放因子（原图尺寸 / 模型输入尺寸）
        pad_param: 填充参数 [top, bottom, left, right]
    Returns:
        Tuple: 转换后的坐标 (x_actual, y_actual, conf)
    """
    x, y, conf = point
    top, bottom, left, right = pad_param
    
    # 去除填充并缩放
    x_actual = (x - left) / scale_factor
    y_actual = (y - top)  / scale_factor
    
    return (x_actual, y_actual, conf)
def parse_args():
    parser = ArgumentParser()
    parser.add_argument('--camera-id', type=int, default=0, help='Camera device ID (default: 0)')
    parser.add_argument('--frame-size', nargs=2, type=int, default=[1280, 720],
                        help='Frame processing size [width, height] (default: 1280 720)')
    parser.add_argument('--config', default="yolov8s_old7cls_640.py",
                        help='Main config file')
    parser.add_argument('--checkpoint', '-c', default="yolov8v6_only.onnx",
                        help='Main checkpoint file')
    parser.add_argument('--line_config', default="line_in_five_head.py",
                        help='Line detection config file')
    parser.add_argument('--line_checkpoint', '-l', default="yolov8v13_only.onnx",
                        help='Line detection checkpoint file')
    parser.add_argument('--out-dir', default='camera_output',
                        help='Path to output file (unused in camera mode)')
    parser.add_argument('--device', default='cuda:0',
                        help='Device used for inference')
    parser.add_argument('--fps-display', action='store_true',
                        help='Show FPS on output')
    parser.add_argument('--outlier-mode', choices=['filter', 'correct'], default='correct',
                        help='异常点处理模式: filter=删除异常点, correct=修正异常点距离 (default: correct)')
    parser.add_argument('--camera-outlier-mode', choices=['filter', 'correct'], default='correct',
                        help='相机坐标异常点处理模式: filter=删除异常点, correct=修正异常点 (default: correct)')
    parser.add_argument('--window-size', type=int, default=20,
                        help='滑动窗口大小 (default: 10)')
    parser.add_argument('--threshold-ratio', type=float, default=1,
                        help='阈值比例 (default: 1.3)')
    parser.add_argument('--prominence', type=float, default=10,
                        help='峰值突出度 (default: 10)')
    parser.add_argument('--neighbor-window', type=int, default=10,
                        help='异常点校正时使用的邻居窗口大小 (default: 2)')
    return parser.parse_args()

def load_model(checkpoint_path, device):
    if checkpoint_path.endswith('.onnx'):
        model = ORTWrapper(checkpoint_path, device)
    model.to(device)
    return model

def build_preprocessing_pipeline():
    return {
        'pipeline': resize_and_pad,
        'preprocessor': preprocess(),
    }

def preprocess_image(rgb_img, pipeline, preprocessor, device):
    data_dict = dict(img=rgb_img, img_id=0)
    processed = pipeline(rgb_img)
    data, scale,pad_param= processed
    scale = 1.0/scale
    data = preprocessor(data).to(device)
    samples ={"scale":scale,"pad_param":pad_param}

    return data, samples

def process_detection_results(feats, device, test_cfg, original_shape, scale_factor, pad_param):
    bbox_preds = []
    cls_scores = []
    
    for feat in feats:
        bbox_pred, cls_score = torch.split(feat, [64, 7], dim=1)
        bbox_preds.append(bbox_pred)
        cls_scores.append(cls_score)
    
    if bbox_preds[0].shape[1] == 64:
        proj = torch.arange(16, dtype=torch.float).to(device)
        bbox_preds = [reg_max2bbox(bbox, proj) for bbox in bbox_preds]
    # print(original_shape)
    batch_img_metas = [{
        'ori_shape': original_shape,
        'pad_shape': (640, 640, 3),
        'scale_factor': scale_factor,
        "pad_param": pad_param
    }]
    
    return predict_by_feat(
        cls_scores, 
        bbox_preds, 
        objectnesses=None,
        batch_img_metas=batch_img_metas,
        cfg=test_cfg,
        post_processing=_bbox_post_process
    )

def _preprocess_image(img: np.ndarray, calibration_mapx_path, calibration_mapy_path) -> np.ndarray:
        """预处理图像（去畸变）"""
        
        calibration_mapx = np.fromfile(calibration_mapx_path, 
                    dtype=np.float32).reshape(img.shape[0], img.shape[1])
        calibration_mapy = np.fromfile(calibration_mapy_path, 
                    dtype=np.float32).reshape(img.shape[0], img.shape[1])
        processed_img = cv2.remap(img, calibration_mapx, calibration_mapy, cv2.INTER_LINEAR)
        print("使用标定映射表处理图像")
        
            
        return processed_img

def _initialize_physical_processor() -> None:
        """初始化物理距离处理器"""
        # with open(self.config.size_ranges_config, 'r') as f:
        #     size_config = yaml.safe_load(f)

        physical_processor = DetectionProcessor()
        # physical_processor.set_size_ranges_config(size_config)
        return physical_processor

def _initialize_camera_converter() -> CameraCoordinateConverter:
        """初始化相机坐标转换器"""
        camera_config_path = "pixel_to_physical_py/config/calib_intrix_new.yaml"
        camera_converter = CameraCoordinateConverter(camera_config_path)
        return camera_converter

def _convert_points_to_detection_results(points_data: List[Tuple], point_size: int = 10) -> List[DetectionResult]:
        """
        将点检测结果转换为DetectionResult列表

        Args:
            points_data: 点检测结果列表，每个元素为 (x_actual, y_actual, conf)
            point_size: 点周围的边界框大小（像素）

        Returns:
            转换后的检测结果列表
        """
        detection_results = []

        if not points_data or len(points_data) == 0:
            return detection_results

        for i, (x_actual, y_actual, conf) in enumerate(points_data):
            # 为点创建一个小的边界框用于测距计算
            bbox_obj = BBox(
                x1=int(x_actual),
                y1=int(y_actual ),
                x2=int(x_actual ),
                y2=int(y_actual )
            )

            # 创建DetectionResult对象
            det_result = DetectionResult(
                bbox=bbox_obj,
                label=f"point_{i}",  # 给点一个标签
                confidence=float(conf)
            )

            detection_results.append(det_result)

        return detection_results

import numpy as np
from scipy.signal import find_peaks

def detect_more_bulges(data, window_sizes=[5, 10, 15], threshold_ratios=[1.2, 1.3, 1.4]):
    all_bulges = []
    
    # 多尺度检测
    for ws in window_sizes:
        for tr in threshold_ratios:
            bulges = []
            for i in range(len(data)):
                left = max(0, i - ws)
                right = min(len(data), i + ws)
                window = data[left:right]
                local_mean = np.mean(window)
                local_std = np.std(window)
                
                # 动态阈值：均值 + n倍标准差
                threshold = local_mean + tr * local_std
                
                if data[i] > threshold:
                    bulges.append((i, data[i]))
            all_bulges.extend(bulges)
    
    # 去重并排序
    unique_bulges = list(set(all_bulges))
    unique_bulges.sort()
    return unique_bulges


def detect_bulges_sliding_window(data, window_size=5, threshold_ratio=1.3):
    """
    滑动窗口局部阈值检测隆起部分

    参数:
        data (list): 距离数据
        window_size (int): 滑动窗口大小
        threshold_ratio (float): 阈值比例

    返回:
        bulges (list): 隆起点的索引列表
    """
    bulges = []
    for i in range(len(data)):
        left = max(0, i - window_size)
        right = min(len(data), i + window_size)
        window = data[left:right]
        local_mean = np.mean(window)
        if data[i] > local_mean * threshold_ratio:
            bulges.append(i)
    return bulges

def detect_bulges_peaks(data, prominence=10):
    """
    峰值检测隆起部分

    参数:
        data (list): 距离数据
        prominence (float): 峰值突出度

    返回:
        peaks (list): 峰值点的索引列表
    """
    peaks, _ = find_peaks(data, prominence=prominence)
    return peaks.tolist()

def find_outlier_indices(distance_list, kept_indices, window_size=10, threshold_ratio=1.3, prominence=10):
    """
    基于滑动窗口和峰值检测方法找出异常点的索引

    参数:
        distance_list (list of str or float): 原始距离序列
        window_size (int): 滑动窗口大小（默认 10）
        threshold_ratio (float): 阈值比例（默认 1.3）
        prominence (float): 峰值突出度（默认 10）

    返回:
        outlier_indices (list of int): 异常值的索引位置
    """
    data = [float(x) for x in distance_list]
    # data = [data[i] for i in kept_indices]
    
    data = [(i , data[i]) for i in kept_indices]

    # # window_sizes = [15, 35, 45]  # 不同窗口大小
    # # threshold_ratios = [1, 1.2, 1.3]  # 更低的阈值

    # # bulges_window = detect_more_bulges(data, window_sizes, threshold_ratios)
    # # 方法1：滑动窗口局部阈值检测
    # bulges_window = detect_bulges_sliding_window(data, window_size, threshold_ratio)

    # # # 方法2：峰值检测
    # # bulges_peaks = detect_bulges_peaks(data, prominence)

    # # 合并结果（取并集）
    # # all_bulges = list(set(bulges_window + bulges_peaks))


    # all_bulges = list(set(bulges_window))

    # all_bulges.sort()  # 按索引排序

    # return all_bulges
    # return bulges_window


    # bulges = detect_bulges_lof(data, n_neighbors=40)
    bulges = detect_bulges_isolation_forest(data)


    return bulges



# def correct_outliers_by_neighbors(distance_list, outlier_indices, window=2):
#     """
#     用周围非异常值的平均值校正给定的异常点

#     参数:
#         distance_list (list of str or float): 原始距离序列
#         outlier_indices (list of int): 异常值的索引位置
#         window (int): 向前向后各取多少个非异常值（默认 2）

#     返回:
#         corrected (list of float): 校正后的距离序列
#     """
#     data = np.array([float(x) for x in distance_list])
#     corrected = data.copy()

#     # 全部异常点用于跳过异常邻居
#     outlier_set = set(outlier_indices)

#     for idx in outlier_indices:
#         neighbors = []

#         # 向前查找
#         i = idx - 1
#         while i >= 0 and len(neighbors) < window:
#             if i not in outlier_set:
#                 neighbors.append(data[i])
#             i -= 1

#         # 向后查找
#         i = idx + 1
#         while i < len(data) and len(neighbors) < 2 * window:
#             if i not in outlier_set:
#                 neighbors.append(data[i])
#             i += 1

#         # 平均值替代
#         if neighbors:
#             corrected[idx] = np.mean(neighbors)

#     return corrected.tolist()


import numpy as np

def correct_outliers_by_neighbors(distance_list, outlier_indices, window=2):
    """
    用周围非异常值的平均值校正给定的异常点，无法校正的异常点设为0

    参数:
        distance_list (list of str or float): 原始距离序列
        outlier_indices (list of int): 异常值的索引位置
        window (int): 向前向后各取多少个非异常值（默认 2）

    返回:
        corrected (list of float): 校正后的距离序列（无法校正的点设为0）
    """
    data = np.array([float(x) for x in distance_list])
    corrected = data.copy()
    outlier_set = set(outlier_indices)

    for idx in outlier_indices:
        neighbors = []
        
        # 向前查找 window 个非异常值
        i = idx - 1
        collected = 0
        while i >= 0 and collected < window:
            if i not in outlier_set:
                neighbors.append(data[i])
                collected += 1
            i -= 1

        # 向后查找 window 个非异常值
        i = idx + 1
        collected = 0
        while i < len(data) and collected < window:
            if i not in outlier_set:
                neighbors.append(data[i])
                collected += 1
            i += 1

        # 如果有邻居则用平均值校正，否则设为0
        corrected[idx] = np.mean(neighbors) if neighbors else 0

    return corrected.tolist()


from sklearn.neighbors import LocalOutlierFactor

def detect_bulges_lof(distances, n_neighbors=40):
    X = np.array(distances).reshape(-1, 1)
    lof = LocalOutlierFactor(n_neighbors=n_neighbors, novelty=False, contamination="auto")
    outliers = lof.fit_predict(X)
    bulge_indices = np.where(outliers == -1)[0]  # -1表示异常
    return [(i, distances[i]) for i in bulge_indices]

from sklearn.ensemble import IsolationForest

# def detect_bulges_isolation_forest(distances):
#     X = np.array(distances).reshape(-1, 1)
#     clf = IsolationForest(contamination=0.1, random_state=60)
#     # clf = IsolationForest(contamination=0.25, random_state=60)
#     outliers = clf.fit_predict(X)
#     bulge_indices = np.where(outliers == -1)[0]
#     return [(i, distances[i]) for i in bulge_indices]


def detect_bulges_isolation_forest(distances):
    # 提取距离值（不要索引）
    X = np.array([dist for (i, dist) in distances]).reshape(-1, 1)
    
    # 训练 Isolation Forest
    clf = IsolationForest(contamination=0.1, random_state=60)
    outliers = clf.fit_predict(X)
    
    # 获取异常点的索引（在原始 distances 中的位置）
    bulge_indices = np.where(outliers == -1)[0]
    
    # 返回 (原索引, 距离值) 的列表
    return [distances[i] for i in bulge_indices]

def is_bulge_by_y(z_mean, y_val):
    """
    根据 z 均值所在区间，判断相机坐标 y 是否超过隆起阈值
    """
    if z_mean <= 25:          # 0–20 cm
        return y_val > 8
    elif z_mean <= 30:        # 20–30 cm
        return y_val > 8.13
    elif z_mean <= 40:        # 30–40 cm
        return y_val > 8.3
    elif z_mean <= 50:        # 40–50 cm
        return y_val > 8.9
    else:                       # >50 cm，可按需拓展
        return y_val > 9.3

def filter_bulge_points(coords: List[CameraCoordinate]):
    # 提取 z 值用于计算均值（剔除 z<=0 的点）
    z_vals = np.array([p.z for p in coords if p.z > 0])
    if len(z_vals) == 0:
        return [], list(range(len(coords)))  # 全部无效
    
    z_mean = z_vals.mean()
    print(f"z_mean: {z_mean}")
    z_min = min(z_vals)
    print(f"z_min: {z_min}")
    z_midean = np.median(z_vals)
    print(f"z_midean: {z_midean}")
    bulge_indices = []
    kept_indices = []

    for idx, p in enumerate(coords):
        if p.z <= 0:
            continue
        if is_bulge_by_y(z_mean, p.y):
            bulge_indices.append(idx)
        else:
            kept_indices.append(idx)

    return kept_indices, bulge_indices

def find_large_change_points(y_sequence, threshold=10):
    """
    找到Y值变化较大的点
    
    参数:
        y_sequence: Y坐标序列(字符串或数字列表)
        threshold: 变化阈值，默认为10
        
    返回:
        包含变化较大点的索引和值的列表
    """
    # 将字符串转换为浮点数
    y_values = [float(y) for y in y_sequence]
    
    change_points = []
    
    for i in range(1, len(y_values)):
        change = abs(y_values[i] - y_values[i-1])
        if change >= threshold:
            change_points.append({
                'index': i,
                'y_value': y_values[i],
                'change': change,
                'prev_y': y_values[i-1]
            })
    
    return change_points

def find_points_near_max(y_sequence, error=5):
    """
    找到全局最大值附近（误差范围内）的所有点
    
    参数:
        y_sequence: Y坐标序列（字符串或数字列表）
        error: 允许的误差范围（默认 ±5）
        
    返回:
        List[Dict]: 包含索引、y值及其与最大值的差值
    """
    # 转换为浮点数
    y_values = [float(y) for y in y_sequence]
    
    # 找到全局最大值
    max_y = max(y_values)
    
    near_max_points = []
    
    for i, y in enumerate(y_values):
        if abs(y - max_y) >= error:
            near_max_points.append({
                'index': i,
                'y_value': y,
                'diff_from_max': max_y - y
            })
    
    return near_max_points

def process_wire_distance_outliers_bulge(camera_coord, valid_results: List[Tuple], outlier_mode: str = 'correct',
                                        window_size: int = 10, threshold_ratio: float = 1.3,
                                        prominence: float = 10, neighbor_window: int = 2) -> List[Tuple]:
    """
    使用滑动窗口和峰值检测方法处理wire检测点的距离异常值（隆起部分）

    Args:
        valid_results: 包含(DetectionResult, point_coords)的列表
        outlier_mode: 处理模式 'filter'=删除异常点, 'correct'=修正异常点距离
        window_size: 滑动窗口大小（默认 10）
        threshold_ratio: 阈值比例（默认 1.3）
        prominence: 峰值突出度（默认 10）
        neighbor_window: 校正时使用的邻居窗口大小（默认 2）

    Returns:
        处理后的结果列表
    """
    if len(valid_results) < 3:
        print("检测点数量不足，跳过异常值处理")
        return valid_results

    # 按照x坐标从左到右排序
    # sorted_results = sorted(valid_results, key=lambda x: x[1][0])  # x[1][0] 是 point_coords 的 x 坐标
    sorted_results = valid_results
    distances = [result[0].physical_distance for result in sorted_results]
    print(f"原始距离序列: {[f'{d:.1f}' for d in distances]}")
    
    pixel_coords = [(result[1][0],result[1][1]) for result in valid_results]
    # 提取 y 坐标
    y_coords = np.array([y for x, y in pixel_coords])
    # print(f"像素坐标(x,y): {[f'({d[0]},{d[1]})' for d in pixel_coords]}")
    print(f"像素坐标y序列: {[f'{d:.1f}' for d in y_coords]}")
    print(f"相机坐标y序列: {[f'{coord.y:.3f}' for coord in camera_coord]}")

    kept_indices, outlier_indices_camera = filter_bulge_points(camera_coord)
    # kept_indices = list(range(len(camera_coord)))
    # distances = [distances_org[i] for i in kept_indices]
    # pixel_coords = [pixel_coords[i] for i in kept_indices]
    # camera_coord = [camera_coord[i] for i in kept_indices]
    # y_coords = [y_coords[i] for i in kept_indices]

    # 使用滑动窗口和峰值检测方法检测异常点（隆起部分）
    outlier_indices = find_outlier_indices(distances,kept_indices, window_size, threshold_ratio, prominence)
    y_indices = find_outlier_indices(y_coords, kept_indices, window_size, threshold_ratio, prominence)
    # y_change = find_large_change_points(y_coords, 10)
    # y_change = find_points_near_max(y_coords, 5)
    # y_change = find_decreasing_segments_with_indices(y_coords, window_size=5, min_decrease=5)
    y_change = find_decreasing_segments_from_peak(y_coords, window_size=5, min_decrease=5)

    # outlier_indices_y = []
    # for i in y_change:
    #     outlier_indices_y.append((i['index'], distances[i['index']]))

    outlier_indices_y = []
    for i in y_change:
        for idx in i['indices']:
            outlier_indices_y.append((idx, distances[idx]))


    outlier_indices1 = []
    # for i in y_indices:
    #     outlier_indices1.append((i[0], distances[i[0]]))
    outlier_indices_camera = [(i, distances[i]) for i in outlier_indices_camera]
    print(f"outlier_indices_camera: {outlier_indices_camera}")
    print(f"outlier_indices_y: {outlier_indices_y}")
    print(f"outlier_indices1: {outlier_indices1}")
    print(f"outlier_indices: {outlier_indices}")
    # outlier_indices_camera = []
    outlier_indices = list(set(outlier_indices + outlier_indices1 + outlier_indices_camera+outlier_indices_y))
    # outlier_indices = list(set(outlier_indices) & set(outlier_indices1))
    outlier_indices.sort()  # 按索引排序
    # final_indices = outlier_indices.copy()
    final_indices = []
    for i,index in enumerate(outlier_indices):
        if index[1] > 30 :
            final_indices.append(index)
        # print(f"异常点索引: {index}, 距离值: {distances[index]:.1f}cm, 像素坐标: {pixel_coords[index]}")
    outlier_indices = final_indices
    # sorted_results = [sorted_results[i] for i in kept_indices]
    if not outlier_indices:
        print("未检测到距离异常点（隆起部分）")
        return sorted_results


    # print(f"滑动窗口+峰值检测到异常点索引: {outlier_indices}")
    # print(f"异常点距离值: {[f'{distances[i]:.1f}' for i in outlier_indices]}")
    # 只提取索引部分
    outlier_indices1 = [idx for (idx, val) in outlier_indices]
    outlier_indices_val = [val for (idx, val) in outlier_indices]
    # print(f"异常点距离值: {[f'{distances[i]:.1f}' for i in outlier_indices]}")
    print(f"异常点距离值: {outlier_indices_val}")
    if outlier_mode == 'filter':
        # 模式1: 删除异常点
        filtered_results = []
        for i, result in enumerate(sorted_results):
            if i not in outlier_indices1:
                filtered_results.append(result)
            else:
                print(f"删除异常点 {i}: 距离={result[0].physical_distance:.1f}cm")

        print(f"过滤后保留 {len(filtered_results)}/{len(sorted_results)} 个点")
        return filtered_results

    elif outlier_mode == 'correct':
        # 模式2: 修正异常点距离
        corrected_distances = correct_outliers_by_neighbors(distances, outlier_indices1, neighbor_window)

        print(f"\n=== 隆起检测距离修正详情 ===")
        correction_count = 0

        for i, (result, corrected_distance) in enumerate(zip(sorted_results, corrected_distances)):
            
            det_result, point_coords = result

            if i in outlier_indices1:
                original_distance = det_result.physical_distance
                det_result.physical_distance = corrected_distance
                det_result.left_distance = corrected_distance
                det_result.right_distance = corrected_distance
                det_result.des = f"[距离已修正] 原值:{original_distance:.1f}cm -> 修正值:{corrected_distance:.1f}cm (隆起检测方法)"

                print(f"修正点 {i}: {original_distance:.1f}cm -> {corrected_distance:.1f}cm")
                correction_count += 1

        print(f"共修正了 {correction_count} 个异常点")
        return sorted_results

    return sorted_results

def compute_3d_distances(valid_results, camera_converter):
    camera_coords = []
    for det_result, point_coords in valid_results:
        d = det_result.physical_distance
        if d <= 0:
            camera_coords.append(None)
        else:
            cam_point = camera_converter.pixel_to_camera_coordinate(point_coords[0], point_coords[1], d)
            camera_coords.append(cam_point)

    # distances = []
    # for i in range(1, len(camera_coords)):
    #     p1 = camera_coords[i - 1]
    #     p2 = camera_coords[i]
    #     if p1 is None or p2 is None:
    #         distances.append(None)
    #     else:
    #         dx = p1.x - p2.x
    #         dy = p1.y - p2.y
    #         dz = p1.z - p2.z
    #         dist = np.sqrt(dx**2 + dy**2 + dz**2)
    #         distances.append(dist)
    # return distances
    return camera_coords
def find_decreasing_segments_with_indices(y_sequence, window_size=5, min_decrease=10):
    """
    找到 y 值持续下降的区间，并返回区间内所有点的索引
    
    参数:
        y_sequence: Y坐标序列（字符串或数字列表）
        window_size: 滑动窗口大小（默认 5）
        min_decrease: 最小下降幅度（默认 10，用于过滤小幅波动）
        
    返回:
        List[Dict]: 每个区间包含 start_idx, end_idx, indices（所有点的索引）, start_y, end_y, decrease
    """
    y_values = [float(y) for y in y_sequence]
    n = len(y_values)
    segments = []
    
    for i in range(n - window_size + 1):
        window = y_values[i:i+window_size]
        is_decreasing = all(window[j] > window[j+1] for j in range(window_size-1))
        total_decrease = window[0] - window[-1]
        
        if is_decreasing and total_decrease >= min_decrease:
            segments.append({
                'start_idx': i,
                'end_idx': i + window_size - 1,
                'indices': list(range(i, i + window_size)),  # 记录窗口内所有索引
                'start_y': window[0],
                'end_y': window[-1],
                'decrease': total_decrease
            })
    
    # 合并重叠或相邻的区间
    merged_segments = []
    if segments:
        current = segments[0]
        for seg in segments[1:]:
            if seg['start_idx'] <= current['end_idx'] + 1:  # 允许相邻区间合并
                # 合并区间
                current['end_idx'] = seg['end_idx']
                current['end_y'] = seg['end_y']
                current['decrease'] = current['start_y'] - seg['end_y']
                current['indices'] = list(range(current['start_idx'], seg['end_idx'] + 1))  # 合并索引
            else:
                merged_segments.append(current)
                current = seg
        merged_segments.append(current)
    
    return merged_segments

# def find_decreasing_segments_with_indices(y_sequence, window_size=5, min_decrease=10, tolerance=0):
#     """
#     找到 y 值非严格下降的区间（允许小幅波动或不变），并返回区间内所有点的索引
    
#     参数:
#         y_sequence: Y坐标序列（字符串或数字列表）
#         window_size: 滑动窗口大小（默认 5）
#         min_decrease: 最小下降幅度（默认 10，用于过滤小幅波动）
#         tolerance: 允许的波动范围（默认 0，设为正数可允许小幅上升）
        
#     返回:
#         List[Dict]: 每个区间包含 start_idx, end_idx, indices, start_y, end_y, decrease
#     """
#     y_values = [float(y) for y in y_sequence]
#     n = len(y_values)
#     segments = []
    
#     for i in range(n - window_size + 1):
#         window = y_values[i:i+window_size]
#         # 检查是否整体下降（允许小幅波动）
#         is_decreasing = True
#         for j in range(window_size - 1):
#             if window[j] < window[j+1] - tolerance:  # 允许小幅上升（tolerance > 0）
#                 is_decreasing = False
#                 break
#         total_decrease = window[0] - window[-1]
        
#         if is_decreasing and total_decrease >= min_decrease:
#             segments.append({
#                 'start_idx': i,
#                 'end_idx': i + window_size - 1,
#                 'indices': list(range(i, i + window_size)),
#                 'start_y': window[0],
#                 'end_y': window[-1],
#                 'decrease': total_decrease
#             })
    
#     # 合并重叠或相邻的区间
#     merged_segments = []
#     if segments:
#         current = segments[0]
#         for seg in segments[1:]:
#             if seg['start_idx'] <= current['end_idx'] + 1:  # 允许相邻区间合并
#                 current['end_idx'] = seg['end_idx']
#                 current['end_y'] = seg['end_y']
#                 current['decrease'] = current['start_y'] - seg['end_y']
#                 current['indices'] = list(range(current['start_idx'], seg['end_idx'] + 1))
#             else:
#                 merged_segments.append(current)
#                 current = seg
#         merged_segments.append(current)
    
#     return merged_segments

import numpy as np

def find_decreasing_segments_from_peak(y_sequence, window_size=5, min_decrease=10, tolerance=0):
    """
    从峰值开始向左右两侧寻找非严格下降区间（允许小幅波动或不变）
    
    参数:
        y_sequence: Y坐标序列（字符串或数字列表）
        window_size: 滑动窗口大小（默认 5）
        min_decrease: 最小下降幅度（默认 10）
        tolerance: 允许的波动范围（默认 0）
        
    返回:
        List[Dict]: 每个区间包含 start_idx, end_idx, indices, start_y, end_y, decrease
    """
    y_values = np.array([float(y) for y in y_sequence])
    peak_idx = np.argmax(y_values)
    n = len(y_values)
    segments = []

    def scan_direction(start, step):
        direction_segments = []
        i = start
        while (0 <= i < n) and (0 <= i + (window_size-1)*step < n):
            window_indices = range(i, i + window_size*step, step)
            window = y_values[window_indices] if step > 0 else y_values[window_indices][::-1]
            
            # 检查是否非严格下降
            is_decreasing = True
            for j in range(window_size - 1):
                if window[j] < window[j+1] - tolerance:
                    is_decreasing = False
                    break
            
            total_decrease = window[0] - window[-1]
            
            if is_decreasing and total_decrease >= min_decrease:
                start_idx = min(i, i + (window_size-1)*step)
                end_idx = max(i, i + (window_size-1)*step)
                direction_segments.append({
                    'start_idx': start_idx,
                    'end_idx': end_idx,
                    'indices': list(range(start_idx, end_idx + 1)) if step > 0 else list(range(end_idx, start_idx + 1))[::-1],
                    'start_y': float(window[0]),
                    'end_y': float(window[-1]),
                    'decrease': total_decrease
                })
                i += window_size * step  # 跳过已检测区间
            else:
                i += step  # 逐步移动
        
        return direction_segments

    # 向左扫描（从峰值前一个点开始）
    left_segments = scan_direction(peak_idx - 1, -1)
    
    # 向右扫描（从峰值后一个点开始）
    right_segments = scan_direction(peak_idx + 1, 1)
    
    # 合并所有区间并按起始位置排序
    all_segments = sorted(left_segments + right_segments, key=lambda x: x['start_idx'])
    
    # 合并重叠或相邻的区间
    merged_segments = []
    if all_segments:
        current = all_segments[0]
        for seg in all_segments[1:]:
            if seg['start_idx'] <= current['end_idx'] + 1:
                # 合并区间
                current['end_idx'] = seg['end_idx']
                current['end_y'] = seg['end_y']
                current['decrease'] = current['start_y'] - seg['end_y']
                current['indices'] = list(range(current['start_idx'], seg['end_idx'] + 1))
            else:
                merged_segments.append(current)
                current = seg
        merged_segments.append(current)
    
    return merged_segments


import math
def sort_points_by_line(points):
    """
    按线条方向重新排序像素坐标点
    :param points: 包含(x,y)坐标的列表，格式可以是元组或列表
    :return: 按线条方向排序后的坐标列表
    """
    if not points:
        return []
    
    # 转换为(x,y)元组列表（如果输入是其他格式）
    coords = [(float(p[0]), float(p[1]), float(p[2])) if isinstance(p, (list, tuple)) else p for p in points]
    
    # 找到两个端点（邻近点数量为1的点）
    endpoints = []
    for point in coords:
        distances = [math.sqrt((p[0]-point[0])**2 + (p[1]-point[1])**2) for p in coords if p != point]
        min_dist = min(distances) if distances else 0
        neighbors = [p for p in coords if math.sqrt((p[0]-point[0])**2 + (p[1]-point[1])**2) <= 1.1 * min_dist and p != point]
        if len(neighbors) == 1:
            endpoints.append(point)
    
    # 如果没有找到端点（闭合线条），选择离中心最远的点作为起始点
    if len(endpoints) < 2:
        center_x = sum(p[0] for p in coords) / len(coords)
        center_y = sum(p[1] for p in coords) / len(coords)
        start_point = max(coords, key=lambda p: (p[0]-center_x)**2 + (p[1]-center_y)**2)
    else:
        # 选择其中一个端点作为起始点（例如x或y最小的）
        start_point = min(endpoints, key=lambda p: (p[0], p[1]))

    sorted_points = [start_point]
    remaining_points = coords.copy()
    remaining_points.remove(start_point)
    
    while remaining_points:
        last_point = sorted_points[-1]
        # 找到离last_point最近的点
        nearest_point = min(remaining_points, 
                          key=lambda p: math.sqrt((p[0]-last_point[0])**2 + (p[1]-last_point[1])**2))
        sorted_points.append(nearest_point)
        remaining_points.remove(nearest_point)
    
    return sorted_points



def main():
    args = parse_args()
    # Create camera capture
    cap = cv2.VideoCapture(args.camera_id)
    cap.set(cv2.CAP_PROP_FOURCC, cv2.VideoWriter_fourcc(*'MJPG'))
    desired_fps = 30
    cap.set(cv2.CAP_PROP_FPS, desired_fps)
# cap.set(cv2.CAP_PROP_FRAME_WIDTH, 1280)
# cap.set(cv2.CAP_PROP_FRAME_HEIGHT, 960)
    # Set camera resolution
    cap.set(cv2.CAP_PROP_FRAME_WIDTH, args.frame_size[0])
    cap.set(cv2.CAP_PROP_FRAME_HEIGHT, args.frame_size[1])
    
    # Create output window
    cv2.namedWindow("监控视角 - 检测结果", cv2.WINDOW_NORMAL)
    cv2.resizeWindow("监控视角 - 检测结果", 1280, 720)

    cv2.namedWindow("监控视角 - 原图", cv2.WINDOW_NORMAL)
    cv2.resizeWindow("监控视角 - 原图", 1280, 720)
    calibration_mapx = "pixel_to_physical_py/config/mapx"
    calibration_mapy = "pixel_to_physical_py/config/mapy"
    physical_processor = _initialize_physical_processor()
    camera_converter = _initialize_camera_converter()

    # Load models
    print("正在加载模型...")
    print(os.path.exists(args.checkpoint))
    model = load_model(args.checkpoint, args.device)
    line_model = load_model(args.line_checkpoint, args.device)
    
    # Build preprocessing pipelines
    print("正在构建预处理流程...")
    main_pp = build_preprocessing_pipeline()
    line_pp = build_preprocessing_pipeline()
    
    # Get configuration
    # test_cfg = main_pp['cfg'].model_test_cfg
    test_cfg = EasyDict(
        max_per_img=300,
        multi_label=True,
        nms=dict(iou_threshold=0.7, type='nms'),
        nms_pre=30000,
        score_thr=0.001)
    
    # FPS calculation
    frame_count = 0
    start_time = time.time()
    
    print("开始实时检测，按ESC键退出...")
    while cap.isOpened():
        # Capture frame-by-frame
        ret, frame = cap.read()
        if not ret:
            print("无法获取帧，退出...")
            # continue
            break
        # Convert BGR to RGB
        rgb_frame = cv2.cvtColor(frame, cv2.COLOR_BGR2RGB)
        original_shape = rgb_frame.shape
        
        rgb_frame = _preprocess_image(rgb_frame, calibration_mapx, calibration_mapy)
        

        # Main detection processing
        main_data, main_samples = preprocess_image(
            rgb_frame, 
            main_pp['pipeline'], 
            main_pp['preprocessor'], 
            args.device
        )
        main_result = model(main_data)
        main_results = process_detection_results(
            main_result,
            args.device,
            test_cfg,
            original_shape,
            main_samples.get('scale', 1),
            main_samples.get('pad_param', np.array([0, 0, 0, 0], dtype=np.float32))
        )
        
        # Line detection processing
        scale_factor = main_samples.get('scale', 1)
        pad_param = main_samples.get('pad_param', np.array([0, 0, 0, 0], dtype=np.float32))
        # Line detection processing
        line_data, line_samples = preprocess_image(
            rgb_frame, 
            main_pp['pipeline'], 
            main_pp['preprocessor'], 
            args.device
        )
        line_result = line_model(line_data)
        line_preds, line_scores = torch.split(line_result[0], [2, 1], dim=1)
        _, line_points, _ = get_single_pred(line_scores, line_preds, (640, 640))
        
        # Visualization - convert back to BGR for display
        # vis_img = visualize_detections(rgb_frame, main_results[0][0], class_names=CLASS_NAMES)
        


        # 处理线检测点并转换坐标
        transformed_points = []
        for point in line_points:
            x, y, conf = point
            if conf > 0.5:
                # 坐标转换
                x_actual, y_actual, conf = transform_point(point, scale_factor, pad_param)
                transformed_points.append((x_actual, y_actual, conf))
                # 在原始图像上绘制圆
                cv2.circle(rgb_frame, (int(x_actual), int(y_actual)), 5, (0, 0, 255), -1)

        transformed_points = sort_points_by_line(transformed_points)
        # if len(transformed_points) > 10:
        #     # print(f"排序后的像素坐标(x,y): {[f'({p[0]},{p[1]})' for p in transformed_points]}")
        #     # 绘制填充的圆圈作为背景
        #     for i,pt in enumerate(transformed_points):
        #         point_x, point_y = (int(pt[0]), int(pt[1]))
        #        # print(point_x, point_y)
        #         cv2.circle(rgb_frame, (point_x, point_y), 6, (0, 255, 0), -1)
        #         # 在圆圈内绘制距离数字（黑色）
        #         # 计算文字位置（居中）
        #         text_size = cv2.getTextSize(f'{i}', cv2.FONT_HERSHEY_SIMPLEX, 0.2, 1)[0]
        #         text_x = point_x - text_size[0] // 2
        #         text_y = point_y + text_size[1] // 2
        #         cv2.putText(rgb_frame, f'{i}', (text_x, text_y),
        #                     cv2.FONT_HERSHEY_SIMPLEX, 0.2, (0, 0, 0), 1)
        # 处理物理距离计算
        detection_results = []
        distance_table_path = "pixel_to_physical_py/config/distance_table"  # 添加距离表路径
        org_frame = rgb_frame.copy()
        if physical_processor is not None and len(transformed_points) > 0:
            try:
                # 将点转换为DetectionResult格式
                detection_results = _convert_points_to_detection_results(transformed_points)
                print(f"\n检测到 {len(detection_results)} 个目标点")

                # 计算所有点的物理距离
                valid_results = []
                for i, det_result in enumerate(detection_results):
                    print(f"\n--- 处理目标点 {i+1}: {det_result.label} ---")
                    # 使用physical_processor处理检测结果
                    physical_processor.process_detection_result(det_result, distance_table_path)

                    # 只保留成功计算出距离的点
                    if det_result.physical_distance > 0:
                        valid_results.append((det_result, transformed_points[i]))

                for i, (det_result, point_coords) in enumerate(valid_results):
                    # 在圆圈内显示距离值
                    point_x, point_y = int(point_coords[0]), int(point_coords[1])
                    distance_value = int(det_result.physical_distance)  # 取整数，更简洁
                    distance_text = f"{distance_value}"

                    border_color = (255, 255, 255)  # 白色边框
                    if det_result.physical_distance < 30:
                            circle_color = (0, 255, 0)  # 绿色 - 近距离
                    elif det_result.physical_distance < 60:
                        circle_color = (255, 255, 0)  # 黄色 - 中距离
                    else:
                        circle_color = (255, 0, 0)  # 红色 - 远距离
                    
                    # 圆圈大小和字体设置
                    circle_radius = 8
                    font_scale = 0.3

                    # 绘制填充的圆圈作为背景
                    cv2.circle(org_frame, (point_x, point_y), circle_radius, circle_color, -1)

                    # 绘制圆圈边框（修正过的点用不同颜色边框）
                    cv2.circle(org_frame, (point_x, point_y), circle_radius, border_color, 2)

                    # 计算文字位置（居中）
                    text_size = cv2.getTextSize(distance_text, cv2.FONT_HERSHEY_SIMPLEX, font_scale, 1)[0]
                    text_x = point_x - text_size[0] // 2
                    text_y = point_y + text_size[1] // 2

                    # 在圆圈内绘制距离数字（黑色）
                    cv2.putText(org_frame, distance_text, (text_x, text_y),
                               cv2.FONT_HERSHEY_SIMPLEX, font_scale, (0, 0, 0), 1)

                # # === 相机坐标转换 ===
                # print(f"\n=== 开始相机坐标转换 ===")
                # camera_coordinates = []
                # detection_points = []
                # distances = []

                # for det_result, point_coords in valid_results:
                #     detection_points.append(point_coords)
                #     distances.append(det_result.physical_distance)

                # if len(detection_points) > 0:
                #     # 转换到相机坐标系
                #     camera_coordinates = camera_converter.convert_detection_points(detection_points, distances)

                #     print(f"转换了 {len(camera_coordinates)} 个点到相机坐标系:")
                #     for i, coord in enumerate(camera_coordinates):
                #         print(f"  点{i+1}: 像素({coord.pixel_x}, {coord.pixel_y}) -> "
                #               f"相机坐标({coord.x:.3f}, {coord.y:.3f}, {coord.z:.3f}cm)")

                #     # 在相机坐标空间中检测异常点
                #     print(f"\n=== 在相机坐标空间中检测异常点 ===")
                #     print(f"处理模式: {args.camera_outlier_mode} ({'删除异常点' if args.camera_outlier_mode == 'filter' else '修正异常点'})")
                #     outlier_infos = camera_converter.detect_outliers_in_camera_space(
                #         camera_coordinates, method='isolation_forest'
                #     )

                #     outlier_count = sum(1 for info in outlier_infos if info.is_outlier)
                #     print(f"检测到 {outlier_count} 个异常点")

                #     if outlier_count > 0:
                #         if args.camera_outlier_mode == 'filter':
                #             # 模式1: 删除相机坐标异常点
                #             print(f"=== 删除相机坐标异常点模式 ===")
                #             filtered_results = []
                #             for i, (outlier_info, (det_result, point_coords)) in enumerate(zip(outlier_infos, valid_results)):
                #                 if not outlier_info.is_outlier:
                #                     filtered_results.append((det_result, point_coords))
                #                 else:
                #                     print(f"  删除异常点{i+1}: 像素({camera_coordinates[i].pixel_x}, {camera_coordinates[i].pixel_y}), "
                #                           f"距离={outlier_info.original_distance:.1f}cm, "
                #                           f"相机坐标=({camera_coordinates[i].x:.3f}, {camera_coordinates[i].y:.3f}, {camera_coordinates[i].z:.3f})")

                #             processed_results = filtered_results
                #             print(f"过滤后保留 {len(processed_results)} 个点")

                #         elif args.camera_outlier_mode == 'correct':
                #             # 模式2: 修正相机坐标异常点
                #             print(f"=== 修正相机坐标异常点模式 ===")
                #             corrected_coordinates = camera_converter.correct_outliers(
                #                 camera_coordinates, outlier_infos, correction_method='neighbor_average'
                #             )

                #             # 更新检测结果中的距离值
                #             for i, (coord, outlier_info) in enumerate(zip(corrected_coordinates, outlier_infos)):
                #                 if outlier_info.is_outlier:
                #                     det_result, point_coords = valid_results[i]
                #                     original_distance = det_result.physical_distance
                #                     corrected_distance = coord.z   # 距离值

                #                     det_result.physical_distance = corrected_distance
                #                     det_result.left_distance = corrected_distance
                #                     det_result.right_distance = corrected_distance
                #                     det_result.des = f"[相机坐标异常修正] 原值:{original_distance:.1f}cm -> 修正值:{corrected_distance:.1f}cm"

                #                     print(f"  修正点{i+1}: {original_distance:.1f}cm -> {corrected_distance:.1f}cm")

                #             # 更新相机坐标列表
                #             camera_coordinates = corrected_coordinates
                #             processed_results = valid_results

                # 处理wire变形导致的距离异常 - 使用隆起检测方法
                if len(valid_results) > 0:
                    print(f"\n=== 开始处理距离异常值 (模式: {args.outlier_mode}, 方法: 隆起检测) ===")
                    print(f"参数: 窗口大小={args.window_size}, 阈值比例={args.threshold_ratio}, " +
                          f"峰值突出度={args.prominence}, 邻居窗口={args.neighbor_window}")
                    camera_coord = compute_3d_distances(valid_results, camera_converter)
                    processed_results = process_wire_distance_outliers_bulge(
                        camera_coord,
                        valid_results,
                        outlier_mode=args.outlier_mode,
                        window_size=args.window_size,
                        threshold_ratio=args.threshold_ratio,
                        prominence=args.prominence,
                        neighbor_window=args.neighbor_window
                    )
                    print(f"处理完成: {len(processed_results)}/{len(valid_results)} 个点保留")
                else:
                    processed_results = valid_results

                # 显示处理后的结果
                print(f"\n=== 可视化 {len(processed_results)} 个处理后的点 ===")
                for i, (det_result, point_coords) in enumerate(processed_results):
                    print(f"目标点 {det_result.label}:")
                    print(f"  物理距离: {det_result.physical_distance:.2f} cm")

                    # 显示相机坐标信息（如果有的话）
                    # if i < len(camera_coordinates):
                    #     coord = camera_coordinates[i]
                    #     print(f"  相机坐标: X={coord.x:.3f}m, Y={coord.y:.3f}m, Z={coord.z:.3f}m")
                    #     print(f"  像素坐标: ({coord.pixel_x}, {coord.pixel_y})")

                    # 检查是否为修正过的点
                    is_corrected = "[距离已修正]" in det_result.des or "[相机坐标异常修正]" in det_result.des
                    if is_corrected:
                        print(f"  ✓ 此点已被修正: {det_result.des}")

                    # 在圆圈内显示距离值
                    point_x, point_y = int(point_coords[0]), int(point_coords[1])
                    distance_value = int(det_result.physical_distance)  # 取整数，更简洁
                    distance_text = f"{distance_value}"

                    border_color = (255, 255, 255)  # 白色边框
                    # 根据距离和是否修正选择颜色
                    if is_corrected:
                        # 修正过的点用特殊颜色标识
                        circle_color = (173, 216, 230)  # 紫色 - 修正过的点
                        # border_color = (255, 255, 0)  # 黄色边框
                        print(f"  绘制修正点: 位置({point_x}, {point_y}), 距离={distance_value}cm, 颜色=紫色")
                    else:
                    # 正常点按距离选择颜色
                        if det_result.physical_distance < 30:
                            circle_color = (0, 255, 0)  # 绿色 - 近距离
                        elif det_result.physical_distance < 60:
                            circle_color = (255, 255, 0)  # 黄色 - 中距离
                        else:
                            circle_color = (255, 0, 0)  # 红色 - 远距离
                        
                        print(f"  绘制正常点: 位置({point_x}, {point_y}), 距离={distance_value}cm")

                    # 圆圈大小和字体设置
                    circle_radius = 8
                    font_scale = 0.3

                    # 绘制填充的圆圈作为背景
                    cv2.circle(rgb_frame, (point_x, point_y), circle_radius, circle_color, -1)

                    # 绘制圆圈边框（修正过的点用不同颜色边框）
                    cv2.circle(rgb_frame, (point_x, point_y), circle_radius, border_color, 2)

                    # 计算文字位置（居中）
                    text_size = cv2.getTextSize(distance_text, cv2.FONT_HERSHEY_SIMPLEX, font_scale, 1)[0]
                    text_x = point_x - text_size[0] // 2
                    text_y = point_y + text_size[1] // 2

                    # 在圆圈内绘制距离数字（黑色）
                    cv2.putText(rgb_frame, distance_text, (text_x, text_y),
                               cv2.FONT_HERSHEY_SIMPLEX, font_scale, (0, 0, 0), 1)
            except Exception as e:
                print(f"物理距离计算出错: {e}")
                import traceback
                traceback.print_exc()
        
        # 在去畸变图像上绘制范围框
        x_min, x_max = (0, 1280)
        y_min, y_max = (500, 700)
        
        # 绘制矩形框 (红色，线宽2)
        cv2.rectangle(rgb_frame, (x_min, y_min), (x_max, y_max), (128, 255, 0), 2)
        cv2.rectangle(org_frame, (x_min, y_min), (x_max, y_max), (128, 255, 0), 2)
        display_img_org = cv2.cvtColor(org_frame, cv2.COLOR_RGB2BGR)
        cv2.imwrite("output/"+str(frame_count)+"_org.png",display_img_org)
        # Convert back to BGR for OpenCV display
        display_img = cv2.cvtColor(rgb_frame, cv2.COLOR_RGB2BGR)
        cv2.imwrite("output/"+str(frame_count)+"_correct.png",display_img)
        # Calculate and display FPS
        frame_count += 1
        if args.fps_display and frame_count % 10 == 0:
            fps = frame_count / (time.time() - start_time)
            cv2.putText(display_img, f"FPS: {fps:.2f}", (20, 50), 
                        cv2.FONT_HERSHEY_SIMPLEX, 1, (0, 255, 0), 2)
        
        # Display the resulting frame
        # cv2.imshow("监控视角 - 检测结果", display_img)
        # # print(scale_factor,pad_param)

        # cv2.imshow("监控视角 - 原图", display_img_org)

        
        cv2.imshow("监控视角 - 检测结果", display_img)
        cv2.imshow("监控视角 - 原图", display_img_org)
        # Exit on ESC key
        key = cv2.waitKey(1)
        if key == 27:  # ESC key
            print("ESC键按下，退出程序...")
            break
    
    # Cleanup
    cap.release()
    cv2.destroyAllWindows()
    print("程序已结束")

if __name__ == '__main__':
    main()